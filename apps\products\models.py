from django.db import models
from django.utils import timezone
from datetime import datetime
# Create your models here.

class Product(models.Model):
    id=models.CharField(
        max_length=8, primary_key=True, editable=False, verbose_name="产品ID"
    )
    code=models.CharField(max_length=16, verbose_name="产品编码")
    name = models.CharField(max_length=32, verbose_name="产品名称")
    category=models.CharField(max_length=8, verbose_name="产品类型")
    description=models.CharField(max_length=32, verbose_name="产品类型")
    status=models.integerField(verbose_name="产品状态")
    create_time = models.DateTimeField(default=timezone.now, verbose_name="创建时间")
    update_time = models.DateTimeField(default=timezone.now, verbose_name="更新时间")

    class Meta:
        verbose_name = "产品"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.id:
            # 获取年份最后两位，P2508
            year = str(datetime.now().year)[-2:]
            mounth = str(datetime.now().month).zfill(2)
            type_prefix = "P" + year+mounth

            # 获取当前类型的最后一个产品
            pd_last = Product.objects.filter(id__startswith=type_prefix).order_by("id").last()
            if pd_last:
                new_number = int(pd_last.id[5:])+1
            else:
                new_number = 1
            
            # 生成新的ID：类型前缀 + 三位序号
            self.id = f"{type_prefix}{new_number:03d}"

        super().save(*args, **kwargs)
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,            
            "code": self.code,
            "type": self.type,
            "category": self.category,
            "description": self.description,            
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S"),            
        }